<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    exit;
}

/**
 * Execute command in isolated environment
 * This function provides multiple isolation techniques to prevent system damage
 */
function executeInIsolation($target) {
    // Rate limiting per session
    if (!isset($_SESSION['ping_count'])) {
        $_SESSION['ping_count'] = 0;
        $_SESSION['ping_reset'] = time() + 60;
    }

    if (time() > $_SESSION['ping_reset']) {
        $_SESSION['ping_count'] = 0;
        $_SESSION['ping_reset'] = time() + 60;
    }

    if ($_SESSION['ping_count'] >= 10) {
        return "Rate limit exceeded. Try again in 1 minute.";
    }

    $_SESSION['ping_count']++;

    // Log the attempt for monitoring
    error_log("Ping challenge attempt: " . $target . " from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));

    // Method 1: Docker isolation (if available)
    if (isDockerAvailable()) {
        return executeInDocker($target);
    }

    // Method 2: Chroot isolation (if available)
    if (isChrootAvailable()) {
        return executeInChroot($target);
    }

    // Method 3: Restricted execution (fallback)
    return executeRestricted($target);
}

function isDockerAvailable() {
    return file_exists('/usr/bin/docker') && is_executable('/usr/bin/docker');
}

function isChrootAvailable() {
    return file_exists('/var/chroot/ping_challenge') && is_dir('/var/chroot/ping_challenge');
}

function executeInDocker($target) {
    // Execute in isolated Docker container
    $escaped_target = escapeshellarg($target);
    $command = "timeout 10 docker run --rm --network none --memory=64m --cpus=0.5 --pids-limit=20 --read-only --tmpfs /tmp:size=10m ping_challenge_container sh -c " . escapeshellarg("ping -c 1 " . $target . " 2>&1 || echo 'Command executed: " . $target . "'");

    $output = shell_exec($command . " 2>&1");
    return $output ?: "Docker execution failed";
}

function executeInChroot($target) {
    // Execute in chroot jail
    $escaped_target = escapeshellarg($target);
    $chroot_dir = "/var/chroot/ping_challenge";
    $command = "timeout 10 sudo chroot " . escapeshellarg($chroot_dir) . " /bin/bash -c " . escapeshellarg("ping -c 1 " . $target . " 2>&1 || echo 'Command executed: " . $target . "'");

    $output = shell_exec($command . " 2>&1");
    return $output ?: "Chroot execution failed";
}

function executeRestricted($target) {
    // Restricted execution with safety checks

    // Block extremely dangerous commands
    $blocked_patterns = [
        '/rm\s+-rf\s+\//',           // rm -rf /
        '/dd\s+if=.*of=\/dev/',      // dd commands to devices
        '/shutdown|reboot|halt/',    // System shutdown
        '/:\(\)\{.*\|\:/',          // Fork bombs
        '/while\s+true/',           // Infinite loops
        '/\/dev\/null.*&/',         // Background processes
        '/nohup/',                  // Background processes
        '/kill\s+-9/',              // Kill processes
        '/mkfs|fdisk/',             // Filesystem commands
        '/iptables|ufw/',           // Firewall commands
    ];

    foreach ($blocked_patterns as $pattern) {
        if (preg_match($pattern, $target)) {
            return "Command blocked for security reasons. Try something else!";
        }
    }

    // Create a temporary restricted environment
    $temp_dir = sys_get_temp_dir() . '/ping_challenge_' . uniqid();
    mkdir($temp_dir, 0755);

    // Create fake files for educational purposes
    file_put_contents($temp_dir . '/flag.txt', 'FLAG{restricted_command_injection}');
    file_put_contents($temp_dir . '/passwd', "root:x:0:0:root:/root:/bin/bash\nchallenger:x:1000:1000::/home/<USER>/bin/bash");
    file_put_contents($temp_dir . '/access.log', 'Fake access log entry');

    // Execute with restrictions
    $escaped_target = escapeshellarg($target);
    $command = "cd " . escapeshellarg($temp_dir) . " && timeout 5 bash -c " . escapeshellarg("ping -c 1 " . $target . " 2>&1 || echo 'Command executed in restricted environment'");

    $output = shell_exec($command . " 2>&1");

    // Cleanup
    array_map('unlink', glob($temp_dir . '/*'));
    rmdir($temp_dir);

    return $output ?: "Restricted execution completed";
}

$challenge_id = 10;
$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$completed = false;
$message = '';
$output = '';

// Handle active redirect from session
if (isset($_SESSION['redirect']) && $_SESSION['redirect_time'] > time()) {

    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $target = $_POST['target'] ?? '';

    $output = executeInIsolation($target);

    if (strpos($output, 'localhost') !== false && strpos($output, '127.0.0.1') !== false) {
        $completed = true;

        $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
        $stmt->execute([$_SESSION['user_id'], $challenge_id]);

        $_SESSION[$session_key] = 0;
        $_SESSION['redirect'] = true;
        $_SESSION['redirect_time'] = time() + 3;
    } else {
        $_SESSION[$session_key]++;
        $message = "❌ please try again.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Command Injection Challenge - Ping | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #eef1f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            background: #fff;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
            width: 500px;
            text-align: center;
        }

        input {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 16px;
        }

        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
        }

        button:hover {
            background: #0056b3;
        }

        pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 6px;
            text-align: left;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .success-message {
            color: green;
            font-weight: bold;
        }

        .error-message {
            color: red;
            background: #ffe5e5;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 10px;
        }

        button[type="button"]:hover {
            background: #0069d9;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>🌐 NetworkTools Ping Service</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>🌐 Scenario:</strong> You're testing NetworkTools' web-based ping utility for network diagnostics. The system executes ping commands directly on the server, which might allow command injection.
        <br><br>
        <strong>🎯 Objective:</strong> Inject additional commands alongside the ping command to execute arbitrary system commands.
    </div>
    <p>Try to ping an IP address. Can you do more than that?</p>

    <?php if ($message): ?>
        <div class="error-message"><?= $message ?></div>
    <?php elseif ($completed): ?>
        <p class="success-message">✅ Challenge completed! Redirecting...</p>
        <?php
        // Clear redirect session state after showing message
        unset($_SESSION['redirect']);
        unset($_SESSION['redirect_time']);
        ?>
    <?php endif; ?>

    <form method="POST">
        <input type="text" name="target" placeholder="127.0.0.1 ?" required>
        <button type="submit">Ping</button>
    </form>

    <?php if ($output): ?>
        <h3>Command Output:</h3>
        <pre><?= htmlspecialchars($output) ?></pre>
    <?php endif; ?>

    <?php if ($_SESSION[$session_key] >= 10 && !$completed): ?>
        <button onclick="toggleHint()" style="margin-top: 20px;">Need a Hint?</button>
        <div id="hint" style="display:none; margin-top: 10px;">
            ✅ <strong>Working Solutions:</strong> Try any of these command injection payloads:<br><br>
            • <code>127.0.0.1; ls</code><br>
            • <code>127.0.0.1 && dir</code><br>
            • <code>127.0.0.1 | whoami</code><br>
            • <code>127.0.0.1; cat /etc/passwd</code>
        </div>
    <?php endif; ?>

    <a href="../../pages/dashboard.php"
        onclick="return confirm('⚠️ Are you sure you want to go back? Any unsolved progress for this challenge will not be recorded.');"
        style="display:inline-block; margin-top: 30px; text-decoration: none;">
        <button type="button" style="background: #6c757d;">Back to Dashboard</button>
    </a>
</div>

<script>
    function toggleHint() {
        const hint = document.getElementById("hint");
        hint.style.display = hint.style.display === "none" ? "block" : "none";
    }

    <?php if ($completed): ?>
    setTimeout(() => {
        alert("Redirecting to the next challenge...");
        window.location.href = "read_file_system.php";
    }, 3000);
    <?php endif; ?>
</script>
</body>
</html>
