<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    exit;
}

/**
 * Ultra-safe simulation of command injection
 * No real shell execution - purely educational simulation
 */
function executeInIsolation($target) {
    // Rate limiting
    if (!isset($_SESSION['ping_count'])) {
        $_SESSION['ping_count'] = 0;
        $_SESSION['ping_reset'] = time() + 60;
    }

    if (time() > $_SESSION['ping_reset']) {
        $_SESSION['ping_count'] = 0;
        $_SESSION['ping_reset'] = time() + 60;
    }

    if ($_SESSION['ping_count'] >= 10) {
        return "Rate limit: 10 attempts per minute. Try again later.";
    }

    $_SESSION['ping_count']++;

    // Log attempt for monitoring
    error_log("Ping challenge attempt: " . $target . " from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));

    // Simulate realistic ping output for educational purposes
    $output = simulatePingCommand($target);

    return $output;
}

/**
 * Simulate command injection responses safely
 */
function simulatePingCommand($input) {
    // Clean input for analysis
    $input = trim($input);

    // Check for command injection patterns
    $injection_patterns = [
        ';', '&&', '||', '|', '`', '$', '$(', '&', '\n', '\r'
    ];

    $has_injection = false;
    foreach ($injection_patterns as $pattern) {
        if (strpos($input, $pattern) !== false) {
            $has_injection = true;
            break;
        }
    }

    // Simulate basic ping response
    if (preg_match('/^127\.0\.0\.1$|^localhost$/', $input)) {
        // Valid localhost ping
        return "PING 127.0.0.1 (127.0.0.1): 56 data bytes\n" .
               "64 bytes from 127.0.0.1: icmp_seq=0 ttl=64 time=0.045 ms\n" .
               "\n--- 127.0.0.1 ping statistics ---\n" .
               "1 packets transmitted, 1 packets received, 0.0% packet loss";
    }

    if (!$has_injection) {
        // Regular IP or hostname - simulate normal ping
        if (filter_var($input, FILTER_VALIDATE_IP)) {
            return "PING $input ($input): 56 data bytes\n" .
                   "64 bytes from $input: icmp_seq=0 ttl=64 time=12.345 ms\n" .
                   "\n--- $input ping statistics ---\n" .
                   "1 packets transmitted, 1 packets received, 0.0% packet loss";
        } else {
            return "ping: cannot resolve $input: Unknown host";
        }
    }

    // Command injection detected - simulate educational responses
    return simulateInjectionResponse($input);
}

/**
 * Simulate command injection responses for educational purposes
 */
function simulateInjectionResponse($input) {
    $output = "";

    // Split by common injection separators
    $parts = preg_split('/[;&|]+/', $input);

    foreach ($parts as $part) {
        $part = trim($part);

        if (empty($part)) continue;

        // Simulate ping part
        if (preg_match('/^127\.0\.0\.1|localhost/', $part)) {
            $output .= "PING 127.0.0.1 (127.0.0.1): 56 data bytes\n";
            $output .= "64 bytes from 127.0.0.1: icmp_seq=0 ttl=64 time=0.045 ms\n";
            $output .= "\n--- 127.0.0.1 ping statistics ---\n";
            $output .= "1 packets transmitted, 1 packets received, 0.0% packet loss\n";
        }
        // Simulate common commands
        elseif (preg_match('/^ls\b|^dir\b/', $part)) {
            $output .= "flag.txt\npasswd\naccess.log\nerror.log\nconfig.php\n";
        }
        elseif (preg_match('/^whoami\b/', $part)) {
            $output .= "www-data\n";
        }
        elseif (preg_match('/^id\b/', $part)) {
            $output .= "uid=33(www-data) gid=33(www-data) groups=33(www-data)\n";
        }
        elseif (preg_match('/^pwd\b/', $part)) {
            $output .= "/var/www/html\n";
        }
        elseif (preg_match('/cat.*passwd/', $part)) {
            $output .= "root:x:0:0:root:/root:/bin/bash\n";
            $output .= "www-data:x:33:33:www-data:/var/www:/usr/sbin/nologin\n";
            $output .= "challenger:x:1000:1000:Challenge User:/home/<USER>/bin/bash\n";
        }
        elseif (preg_match('/cat.*flag/', $part)) {
            $output .= "FLAG{command_injection_successful}\n";
        }
        elseif (preg_match('/^echo\s+(.+)/', $part, $matches)) {
            $output .= $matches[1] . "\n";
        }
        else {
            // Generic command simulation
            $output .= "Command executed: " . htmlspecialchars($part) . "\n";
        }
    }

    return $output;
}

$challenge_id = 10;
$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$completed = false;
$message = '';
$output = '';

// Handle active redirect from session
if (isset($_SESSION['redirect']) && $_SESSION['redirect_time'] > time()) {

    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $target = $_POST['target'] ?? '';

    $output = executeInIsolation($target);

    if (strpos($output, 'localhost') !== false && strpos($output, '127.0.0.1') !== false) {
        $completed = true;

        $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
        $stmt->execute([$_SESSION['user_id'], $challenge_id]);

        $_SESSION[$session_key] = 0;
        $_SESSION['redirect'] = true;
        $_SESSION['redirect_time'] = time() + 3;
    } else {
        $_SESSION[$session_key]++;
        $message = "❌ please try again.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Command Injection Challenge - Ping | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #eef1f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            background: #fff;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
            width: 500px;
            text-align: center;
        }

        input {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 16px;
        }

        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
        }

        button:hover {
            background: #0056b3;
        }

        pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 6px;
            text-align: left;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .success-message {
            color: green;
            font-weight: bold;
        }

        .error-message {
            color: red;
            background: #ffe5e5;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 10px;
        }

        button[type="button"]:hover {
            background: #0069d9;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>🌐 NetworkTools Ping Service</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>🌐 Scenario:</strong> You're testing NetworkTools' web-based ping utility for network diagnostics. The system executes ping commands directly on the server, which might allow command injection.
        <br><br>
        <strong>🎯 Objective:</strong> Inject additional commands alongside the ping command to execute arbitrary system commands.
    </div>
    <p>Try to ping an IP address. Can you do more than that?</p>

    <?php if ($message): ?>
        <div class="error-message"><?= $message ?></div>
    <?php elseif ($completed): ?>
        <p class="success-message">✅ Challenge completed! Redirecting...</p>
        <?php
        // Clear redirect session state after showing message
        unset($_SESSION['redirect']);
        unset($_SESSION['redirect_time']);
        ?>
    <?php endif; ?>

    <form method="POST">
        <input type="text" name="target" placeholder="127.0.0.1 ?" required>
        <button type="submit">Ping</button>
    </form>

    <?php if ($output): ?>
        <h3>Command Output:</h3>
        <pre><?= htmlspecialchars($output) ?></pre>
    <?php endif; ?>

    <?php if ($_SESSION[$session_key] >= 10 && !$completed): ?>
        <button onclick="toggleHint()" style="margin-top: 20px;">Need a Hint?</button>
        <div id="hint" style="display:none; margin-top: 10px;">
            ✅ <strong>Working Solutions:</strong> Try any of these command injection payloads:<br><br>
            • <code>127.0.0.1; ls</code><br>
            • <code>127.0.0.1 && dir</code><br>
            • <code>127.0.0.1 | whoami</code><br>
            • <code>127.0.0.1; cat /etc/passwd</code>
        </div>
    <?php endif; ?>

    <a href="../../pages/dashboard.php"
        onclick="return confirm('⚠️ Are you sure you want to go back? Any unsolved progress for this challenge will not be recorded.');"
        style="display:inline-block; margin-top: 30px; text-decoration: none;">
        <button type="button" style="background: #6c757d;">Back to Dashboard</button>
    </a>
</div>

<script>
    function toggleHint() {
        const hint = document.getElementById("hint");
        hint.style.display = hint.style.display === "none" ? "block" : "none";
    }

    <?php if ($completed): ?>
    setTimeout(() => {
        alert("Redirecting to the next challenge...");
        window.location.href = "read_file_system.php";
    }, 3000);
    <?php endif; ?>
</script>
</body>
</html>
